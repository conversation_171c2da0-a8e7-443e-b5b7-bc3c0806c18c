@charset"utf-8";@import url("reset.css");body{background:#fff url(bg.png) repeat-x top}body,td,th,input,select,textarea{color:#555;font:12px/1.5 "微软雅黑"}a{color:#555;text-decoration:none}a:hover{color:#f30;text-decoration:underline}.blank10{clear:both;display:block;height:10px;width:100%}#topbg{height:32px;line-height:32px}#topbar{margin:0 auto;width:1200px}#topbar-left{float:left;font-size:12px}#topbar-right{color:#ccc;float:right;font-size:12px}#topbar-right img{vertical-align:middle}#wrapper{margin:0 auto;width:1200px}#topbox{height:100px;position:relative}.logo{background:url(logo.png) center;background-size:contain;display:block;float:left;height:100px;width:220px;position:relative;overflow:hidden}.logo::before{content:'';position:absolute;top:0;left:-50%;width:10%;height:100%;background:linear-gradient(to right,transparent 0%,rgba(255,255,255,.1)30%,rgba(255,255,255,.6)50%,rgba(255,255,255,.1)70%,transparent 100%);filter:blur(8px);animation:sweep 3s ease-in-out infinite;transform:rotate(15deg);transform-origin:center}@keyframes sweep{0%{left:-50%}50%{left:50%}100%{left:100%}}#sobox{float:right;padding-top:20px}.sofrm{display:block;margin:0 auto;padding-top:12px;position:relative}.sipt{background:url(ipt.png) no-repeat top left;border:1px solid #dadada;display:block;font:normal 13px/30px normal;float:left;height:30px;padding:0 5px 0 90px;width:300px}.sbtn{background:#65bc0b;border:0;color:#fff;cursor:pointer;font-size:12px;height:32px;width:70px}#selopt{background:url(select.gif) no-repeat;position:absolute;left:2px;top:17px;width:88px}#cursel{cursor:pointer;display:block;height:28px;line-height:28px;overflow:hidden;text-indent:10px;width:85px;font-size:13px}#options{border:1px solid #dadada;border-top:0;display:none;list-style:none;position:absolute;left:-2px;width:80px;z-index:1000;background:#fff}#options li{background:#fff;clear:both;cursor:pointer}#options li a{color:#555;display:block;height:25px;line-height:25px;text-decoration:none;text-align:center;font-size:13px}#options li a:hover{background:#1791de;color:#fff}.current{background:#1791de;color:#fff;display:block;text-decoration:none}#navbox{background:url(blue.png) repeat-x;display:block;height:35px}.navbar li{float:left;font:14px/35px "微软雅黑";height:35px;text-align:center;width:100px}.navbar li a{display:block;color:#fff}.navbar li a:hover{background:#0080c6;color:#fff}.navbar .navline{background:#0797e5;display:block;height:35px;width:1px}.navbar .cur{background:#0067ae}#txtbox{background:url(blue.png) repeat-x 0 -55px;border-left:1px solid #dae7ed;border-right:1px solid #dae7ed;height:40px}.count{float:left;padding:10px;font-size:13px}.count b{color:#f60;font:bold 16px Arial;padding-right:3px}.link{color:#999;float:right;padding:10px}.link a{color:#06c}#quickbox{background:#f9fef4;border:1px dashed #cbe6bd;overflow:hidden;padding:6px;white-space:nowrap}#quickbox a{margin-right:15px}#homebox-left{float:left;width:310px}#homebox-right{float:right;width:880px}#hcatebox{background:#f8fdff;border:1px solid #dae7ed;padding:8px}#hcatebox dt{clear:both;display:block;font:bold 14px/25px "微软雅黑";height:25px}#hcatebox dt a{color:#07c}.hcatelist{list-style:none;padding:0;margin:0;display:flex;flex-wrap:wrap;padding-top:6px}.hcatelist li{margin-right:3px;margin-bottom:5px}.hcatelist li a{display:block;padding:2px 4px;background:#fff;border:1px solid #ddd;border-radius:5px;font-size:13px;color:#333}.hcatelist li a:hover{background:#e9ecef;text-decoration:none}#newbox{border:1px solid #dae7ed}#newbox h3{background:#f8fdff;border-bottom:1px dashed #dae7ed;color:#07c;font-size:14px;padding:6px}.newlist{padding:3px 8px}.newlist li{padding:5px 0;white-space:nowrap}.newlist li img{top:4px;position:relative;margin-right:4px}.newlist li a{display:block;overflow:hidden;font-size:13px;}.newlist li span{color:#aea8a8;float:right;margin-top:6px}#bestbox{border:1px solid #dae7ed}#bestbox h3{background:#f8fdff;border-bottom:1px solid #dae7ed;font:bold 14px normal;height:30px}#bestbox h3 span{background:#fff;border:1px solid #cedee6;border-bottom:0;color:#07c;display:block;float:left;height:25px;line-height:25px;margin-left:5px;margin-top:5px;text-align:center;width:80px}.bestlist{padding:8px}.bestlist li{display:block;float:left;height:30px;line-height:30px;margin-right:12px;overflow:hidden;white-space:nowrap;width:105px;box-shadow:0 2px 8px rgba(0,0,0,.06)}.bestlist li img{top:4px;position:relative;margin-right:6px}.bestlist li a{font-size:13px;color:#f31818}#coolbox{border:1px solid #dae7ed}#coolbox h3{background:#f8fdff;border-bottom:1px dashed #dae7ed;color:#07c;font-size:14px;padding:6px}.csitelist{padding:5px 8px}.csitelist li{display:block;font-size:14px;height:30px;overflow:hidden;vertical-align:top;width:100%}.csitelist li h4{display:block;float:left;font-weight:400;height:30px;line-height:30px;width:70px}.csitelist li h4 a{color:#07c}.csitelist li span{display:block;float:left;height:30px;line-height:30px;margin-right:10px;overflow:hidden;white-space:nowrap;width:82px}.csitelist li span img{top:4px;position:relative;margin-right:6px}.csitelist li span a{font-size:13px}.csitelist .more{color:#07c;float:right;font-size:12px;line-height:30px}.sline{background:url(dot.gif) repeat-x center;display:block;height:10px}#rowbox{border:1px solid #dae7ed;padding:10px;height:422px}#newsbox{float:left;width:50%}#newsbox h3{color:#07c;font-size:14px;padding-bottom:6px}.newslist li{padding:6px 0;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.newslist li span{color:#aea8a8;float:right;font-size:11px;margin-top:2px}#exlink{float:right;width:410px}#exlink h3{color:#07c;font-size:14px;padding-bottom:6px}.exlist li{padding:6px 0;white-space:nowrap;overflow:hidden}.exlist li span{color:#aea8a8;float:right;font-size:11px}.line{border-left:1px dashed #dadada;float:left;height:342px;margin-left:10px;width:1px}#inbox,#linkbox{background:#f8fdff;border:1px solid #dae7ed;border-radius:5px;padding:10px}#inbox h3,#linkbox h3{font-size:14px;margin-bottom:10px;color:#07c}.inlist,.linklist{list-style:none;padding:0;margin:0;display:flex;flex-wrap:wrap}.inlist li,.linklist li{margin-right:6px;margin-bottom:10px}.inlist li img{top:4px;position:relative;margin-right:6px}.inlist li a,.linklist li a{display:block;background:#fff;border:1px solid #ddd;border-radius:5px;font-size:13px;color:#333;width:99.5px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.inlist li a:hover,.linklist li a:hover{background:#e9ecef;text-decoration:none}#inbox1{border:1px solid #dae7ed}#inbox1 h3{background:#f8fdff;border-bottom:1px dashed #dae7ed;color:#07c;font-size:14px;padding:6px}.inlist1 li{float:left;white-space:nowrap;overflow:hidden;text-align:center;width:128px;border:1px solid #eee;padding:5px;margin:5px;}.inlist li{float:left;height:23px;line-height:23px;white-space:nowrap}#linkbox{background:#f8fdff;border:1px solid #dae7ed;padding:5px 8px}#linkbox h3{float:left;height:23px;line-height:23px;width:60px}.linklist li{float:left;height:23px;line-height:23px;margin-right:20px;vertical-align:top;white-space:nowrap}#footer{background:url(fbg.png) repeat-x;padding:10px;text-align:center}#fmenu{color:#ccc;padding-bottom:5px}#fmenu a{text-decoration:none}#fmenu a:hover{color:#f60;text-decoration:underline}#fcopy{line-height:23px}.newslist{list-style:none;padding-left:0;counter-reset:rank}.newslist li{position:relative;padding-left:26px;counter-increment:rank}.newslist li::before{content:counter(rank);position:absolute;left:5px;top:50%;transform:translateY(-50%);font:700 18px/1 'Roboto Condensed',Arial,sans-serif;color:#666}.newslist li:nth-child(1)::before{color:#ff4757;font-size:22px}.newslist li:nth-child(2)::before{color:#ffa502;font-size:20px}.newslist li:nth-child(3)::before{color:#2ed573;font-size:18px}.newslist li:nth-child(n+4)::before{color:#a4b0be;font-weight:500}.site-notice ul{padding:0;margin:0}.site-notice{overflow:hidden;line-height:30px;height:22px;width:670px;float:left}.site-notice li{padding-left:10px;height:30px}#newsbox span1 a{float:right;margin-top:-24px;color:#07c}.arcbox-list{font-size:13px}.arcbox-list li{padding:2px 0}.arcbox-list li strong,.arcbox-list li b{padding:0 3px;background:#e74c3c;color:#fff;margin-right:13px}.arcbox-list li a{padding:0 8px;color:#555}.newbox .arcbox-list li a{padding:0 2px!important}.uzkoo{display: inline-block;background: linear-gradient(45deg, #fd8434, #ffa994, #fd3c3c, #ff9e88, #ff6060, #ff146e, #ff96df);background-size: 400% 400%;animation: gradientAnimation 10s ease infinite;border-radius: 0.4rem;margin-right: 1rem;margin: 0.5rem 0;padding:0.5rem}.uzkoo a {color: #fff;font-weight: bold;text-decoration: none;}.vip-title{display:inline-block;font-family:'Arial Black',sans-serif;font-size:18px;font-weight:bold;background:linear-gradient(45deg,#FFD700,#C0C0C0,#FFD700);-webkit-background-clip:text;background-clip:text;color:transparent;text-shadow:0 0 10px rgba(255,215,0,0.5),0 0 20px rgba(255,215,0,0.3);animation:glow 2s ease-in-out infinite alternate;letter-spacing:2px;position:relative}@keyframes glow{from{text-shadow:0 0 5px rgba(255,215,0,0.3),0 0 10px rgba(255,215,0,0.2)}to{text-shadow:0 0 10px rgba(255,215,0,0.5),0 0 20px rgba(255,215,0,0.4),0 0 30px rgba(255,215,0,0.2)}}

/* 渐变动画定义 */
@keyframes gradientAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}.donate-button{position:relative;overflow:hidden;margin-top:-4px}.donate-button:hover{background-color:#e55a00}.shine-effect{position:absolute;top:0;left:-100%;width:50%;height:100%;background:linear-gradient(to right,transparent,rgba(255,255,255,0.3),transparent);transform:skewX(-25deg);animation:shine 2s infinite}@keyframes shine{0%{left:-100%}100%{left:100%}}#donate-popup{position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);z-index:1000;display:flex;justify-content:center;align-items:center}.donate-popup-content{background:#fff;padding:20px;border-radius:8px;width:80%;max-width:600px;text-align:center;position:relative}.donate-popup-content .close{position:absolute;top:10px;right:10px;font-size:24px;cursor:pointer;color:#666}.donate-popup-content h3{margin:0 0 20px 0;color:#333;font-size:18px}.donate-qr-codes{display:flex;justify-content:space-around;flex-wrap:wrap}.donate-qr-codes div{margin:10px;text-align:center}.donate-qr-codes h4{margin:10px 0;color:#555;font-size:16px}.donate-qr-codes img{width:150px;height:200px;border:1px solid #ddd;border-radius:4px}#bestbox ul li {margin: 3px;border-radius: 5px;}.inlist1 li{display:inline-block;background:#fff;border-radius:6px;box-shadow:0 1px 4px rgba(0,0,0,.15);transition:transform .25s,box-shadow .25s;position:relative}.inlist1 li:hover{transform:translateY(-6px) scale(1.03);box-shadow:0 8px 20px rgba(0,0,0,.25);z-index:2}.inlist1 li img.thumb{border-radius:4px}/* 桌面端默认样式 - 数据归档月份链接 */
.archive-month-link {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px;
    border-radius: 4px;
    text-decoration: none;
    color: #666;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 13px; /* 增大字体 */
    font-weight: 500;
}

.archive-month-link:hover {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 300%;
    animation: gradientAnimation 2s ease infinite;
    color: white;
    text-decoration: none;
    border-color: transparent;
    transform: scale(1.05);
}

.archive-month-link.active {
    background: linear-gradient(45deg, #007cba, #0056b3, #004085);
    background-size: 200% 200%;
    animation: gradientAnimation 3s ease infinite;
    color: white;
    border-color: #007cba;
    font-weight: 600;
}

.archive-month-link.scrolling {
    background: linear-gradient(45deg, #e74c3c, #c0392b, #a93226, #922b21);
    background-size: 300% 300%;
    animation: gradientAnimation 1.5s ease infinite;
    color: white;
    border-color: #e74c3c;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.3);
}

/* 桌面端默认样式 - 文章分类链接 */
.article-cate-link {
    display: inline-block;
    padding: 4px 8px;
    margin: 2px 3px 2px 0;
    font-size: 13px; /* 增大字体 */
    color: #666;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid #ddd;
    font-weight: 500;
}

.article-cate-link:hover {
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
    background-size: 300% 300%;
    animation: gradientAnimation 2s ease infinite;
    color: white;
    text-decoration: none;
    border-color: transparent;
    transform: scale(1.05);
}

.article-cate-link.active {
    background: linear-gradient(45deg, #e74c3c, #c0392b, #a93226);
    background-size: 200% 200%;
    animation: gradientAnimation 3s ease infinite;
    color: white;
    border-color: #e74c3c;
    font-weight: 600;
}

.article-cate-link.scrolling {
    background: linear-gradient(45deg, #f39c12, #e67e22, #d35400, #c0392b);
    background-size: 300% 300%;
    animation: gradientAnimation 1.5s ease infinite;
    color: white;
    border-color: #f39c12;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(243, 156, 18, 0.3);
}

@media(max-width:768px){body{background:#fff}#wrapper{margin:0 auto;width:100%}#navbox{background:#0089d4;display:block;height:106px}.navbar li{float:left;font:14px/35px "微软雅黑";height:35px;text-align:center;width:25%}li.navline,.navbar li:nth-last-child(2){display:none!important}.logo{background:url(logo.png) center;background-size:100% 100%;display:block;float:left;height:60px;width:140px}#topbox{height:60px}#sobox{float:right;padding-top:0}.sipt{background:url(ipt.png) no-repeat top left;border:1px solid #dadada;display:block;font:normal 13px/30px normal;float:left;height:30px;padding:0 5px 0 90px;width:80px}.sofrm{display:block;margin:0 auto;padding-top:14px;position:relative;width:auto}.sbtn{background:#65bc0b;border:0;color:#fff;cursor:pointer;font-size:14px;height:32px;width:50px;margin-right:3px}#homebox,#inbox,#linkbox,#inbox1{margin:1%}#inbox{margin-top:0}#quickbox{background:#ffc;border:1px dashed #f60;overflow:hidden;width:96%;height:50px;margin:0 auto;line-height:25px;padding:1%}#homebox-left{float:left;width:100%;margin-top:-10px}.slxx{float:left;width:99%;height:96px;margin:0;border:1px solid #DBDBDB}.slxx dl{float:left;width:100%;position:relative;padding-top:10px;height:50px}#homebox-right{float:right;width:100%}#newsbox,#exlink{float:left;width:100%}.inlist1 li img{width:100%;height:50px}.bestlist li{display:block;float:left;height:30px;line-height:30px;margin-right:1.3%;overflow:hidden;padding-left:0;white-space:nowrap;width:28.3%}.site-notice{overflow:hidden;line-height:16px;height:40px;width:100%;float:left}.site-notice li{padding-left:5px;padding-right:5px;height:40px}#topbar-left,#topbg,.line{display:none}.inlist li a,.linklist li a{width:81px}#exlink h3{color:#07c;font-size:14px;padding-bottom:6px;margin-top:8px}#bestbox{border:1px solid #dae7ed;margin-top:10px}.inlist1 li{float: left;margin-bottom: 10px;margin-top: 10px;margin-right: 10px;white-space: nowrap;overflow: hidden;text-align: center;width: 115px;border: 1px solid #eee;padding: 0px;}.newlist li a{display: block;overflow: hidden;font-size: 13px;width: 260px;}

/* 移动端样式覆盖 */
.archive-month-link {
    padding: 2px 5px;
    margin: 1px;
    font-size: 10px;
}

.article-cate-link {
    padding: 2px 5px;
    margin: 1px 2px 1px 0;
    font-size: 10px;
}}
@media(max-width:480px){body{background:#fff}#wrapper{margin:0 auto;width:100%}#navbox{background:#0089d4;display:block;height:106px}.navbar li{float:left;font:14px/35px "微软雅黑";height:35px;text-align:center;width:25%}li.navline,.navbar li:nth-last-child(2){display:none!important}.logo{background:url(logo.png) center;background-size:100% 100%;display:block;float:left;height:60px;width:140px}#topbox{height:60px}#sobox{float:right;padding-top:0}.sipt{background:url(ipt.png) no-repeat top left;border:1px solid #dadada;display:block;font:normal 13px/30px normal;float:left;height:30px;padding:0 5px 0 90px;width:80px}.sofrm{display:block;margin:0 auto;padding-top:14px;position:relative;width:auto}.sbtn{background:#65bc0b;border:0;color:#fff;cursor:pointer;font-size:14px;height:32px;width:50px;margin-right:3px}#homebox,#inbox,#linkbox,#inbox1{margin:1%}#inbox{margin-top:0}#quickbox{background:#ffc;border:1px dashed #f60;overflow:hidden;width:96%;height:50px;margin:0 auto;line-height:25px;padding:1%}#homebox-left{float:left;width:100%;margin-top:-10px}.slxx{float:left;width:99%;height:96px;margin:0;border:1px solid #DBDBDB}.slxx dl{float:left;width:100%;position:relative;padding-top:10px;height:50px}#homebox-right{float:right;width:100%}#newsbox,#exlink{float:left;width:100%}.inlist1 li img{width:100%;height:50px}.bestlist li{display:block;float:left;height:30px;line-height:30px;margin-right:1.3%;overflow:hidden;padding-left:0;white-space:nowrap;width:28.3%}.site-notice{overflow:hidden;line-height:16px;height:40px;width:100%;float:left}.site-notice li{padding-left:5px;padding-right:5px;height:40px}#topbar-left,#topbg,.line{display:none}.inlist li a,.linklist li a{width:78px}#exlink h3{color:#07c;font-size:14px;padding-bottom:6px;margin-top:8px}#bestbox{border:1px solid #dae7ed;margin-top:10px}.inlist1 li{float: left;margin-bottom: 10px;margin-top: 10px;margin-right: 10px;white-space: nowrap;overflow: hidden;text-align: center;width: 108px;border: 1px solid #eee;padding: 0px;}.newlist li a{display: block;overflow: hidden;font-size: 13px;width: 260px;}.csitelist li span{width: 74px;}.count{display:none}.uzkoo{display: inline-block;
    background: linear-gradient(45deg, #fd8434, #ffa994, #fd3c3c, #ff9e88, #ff6060, #ff146e, #ff96df);
    background-size: 400% 400%;
    animation: gradientAnimation 10s ease infinite;
    border-radius: 0.4rem;
    margin-right: 1rem;margin: 0.5rem 0;padding: 0.3rem}

/* 480px以下移动端样式覆盖 */
.archive-month-link {
    padding: 2px 5px;
    margin: 1px;
    font-size: 10px;
}

.article-cate-link {
    padding: 2px 5px;
    margin: 1px 2px 1px 0;
    font-size: 10px;
}
}


/* NEW图标样式 - 强制优先级，确保显示 */
span.new-icon, .new-icon, a .new-icon {
    display: inline-block !important;
    background: #ff4757 !important;
    color: #ffffff !important;
    font-size: 10px !important;
    font-weight: bold !important;
    font-family: Arial, sans-serif !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    margin-left: 6px !important;
    margin-right: 2px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.4) !important;
    border: none !important;
    text-decoration: none !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
    position: relative !important;
    animation: newIconBlink 2s ease-in-out infinite !important;
    min-width: 20px !important;
    text-align: center !important;
}

/* 闪烁动画效果 */
@keyframes newIconBlink {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* 悬停效果 */
.new-icon:hover {
    background: #ff3742 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 3px 6px rgba(255, 71, 87, 0.6) !important;
}

/* 响应式调整 */
@media (max-width: 767px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 8px !important;
        padding: 1px 4px !important;
        margin-left: 4px !important;
        border-radius: 3px !important;
    }
}

@media (max-width: 480px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 7px !important;
        padding: 1px 3px !important;
        margin-left: 3px !important;
    }
}

/* NEW图标样式 - 强制优先级，确保显示 */
span.new-icon, .new-icon, a .new-icon {
    display: inline-block !important;
    background: #ff4757 !important;
    color: #ffffff !important;
    font-size: 10px !important;
    font-weight: bold !important;
    font-family: Arial, sans-serif !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    margin-left: 6px !important;
    margin-right: 2px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.4) !important;
    border: none !important;
    text-decoration: none !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
    position: relative !important;
    animation: newIconBlink 2s ease-in-out infinite !important;
    min-width: 20px !important;
    text-align: center !important;
}

/* 闪烁动画效果 */
@keyframes newIconBlink {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* 悬停效果 */
.new-icon:hover {
    background: #ff3742 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 3px 6px rgba(255, 71, 87, 0.6) !important;
}

/* 响应式调整 */
@media (max-width: 767px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 8px !important;
        padding: 1px 4px !important;
        margin-left: 4px !important;
        border-radius: 3px !important;
    }
}

@media (max-width: 480px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 7px !important;
        padding: 1px 3px !important;
        margin-left: 3px !important;
    }
}

/* NEW图标样式 - 使用!important确保优先级 */
.new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    margin-left: 5px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    box-shadow: 0 1px 3px rgba(255, 71, 87, 0.3) !important;
    animation: newPulse 2s infinite !important;
    position: relative !important;
    z-index: 999 !important;
    white-space: nowrap !important;
    line-height: 1 !important;
    border: none !important;
    text-decoration: none !important;
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
}

/* 确保NEW图标在链接中正常显示 */
a .new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
}

/* 悬停效果 */
.new-icon:hover {
    background: linear-gradient(45deg, #ff3742, #ff5865) !important;
    transform: scale(1.1) !important;
}

@keyframes newPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* 响应式调整 */
@media (max-width: 767px) {
    .new-icon {
        font-size: 9px !important;
        padding: 1px 3px !important;
        margin-left: 3px !important;
    }
}

/* 确保在不同容器中都能正常显示 */
.newlist .new-icon,
.quicklist .new-icon,
.newslist .new-icon,
li .new-icon,
a .new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    margin-left: 5px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    animation: newPulse 2s infinite !important;
}