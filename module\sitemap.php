<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$type = trim($_GET['type']);
$cate_id = intval($_GET['cid']);
if (empty($type)) $type = 'all';

switch ($type) {
    case 'webdir':
    case 'weblink':
        get_website_sitemap($cate_id);
        break;
    case 'article':
        get_article_sitemap($cate_id);
        break;
    case 'category':
        get_category_sitemap();
        break;
    case 'all':
    default:
        get_website_sitemap($cate_id);
        break;
}

?>